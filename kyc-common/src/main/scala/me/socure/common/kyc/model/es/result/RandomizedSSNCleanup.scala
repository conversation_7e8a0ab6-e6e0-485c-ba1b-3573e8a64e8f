package me.socure.common.kyc.model.es.result

import me.socure.common.kyc.model.NationalId
import me.socure.common.kyc.model.es.result.RecordsCleanupHelper._
import me.socure.common.kyc.model.es.result.SSNWeightComputer.isRandomlyIssuedNationalIdHelper
import me.socure.common.kyc.util.SSNUtil

/**
 * Cleanup operation to handle randomized vs non-randomized SSNs and DOB selection based on SSN issuance year range.
 *
 * Logic:
 * 1. Exclude randomized SSNs if a non-randomized SSN is also present
 * 2. Exception: If SSN issuance year is on or after 2011 (both SSNs are randomized), do not exclude either SSN
 * 3. Ensure that the excluded SSN does not match the input SSN
 * 4. If input SSN is only 4 digits, match using the last four digits only
 * 5. Select DOB based on SSN issuance year range (SSNLowYear to SSNHighYear):
 *    - First priority: DOBs within the exact SSN issuance year range
 *    - Second priority: DOBs within +1 year of the SSN issuance range
 *    - Third priority: If all DOBs are prior to the SSN issuance year, select the DOB closest to the issuance range
 *    - Handle cases where only low year or high year is available with same priority logic
 *
 * Note: This logic does not apply to ITINs (Individual Taxpayer Identification Numbers).
 */
object RandomizedSSNCleanup extends RecordsCleanupOperation {
  val name = "RandomizedSSNCleanup"

  /**
   * Check if an SSN is an ITIN (Individual Taxpayer Identification Number)
   */
  private def isITIN(ssn: String): Boolean = {
    val ssnDigits = ssn.replaceAll("\\D", "")
    if (ssnDigits.length == 9 && ssnDigits.charAt(0) == '9') {
      val groupNumber = ssnDigits.substring(3, 5).toInt
      (groupNumber >= 50 && groupNumber <= 65) ||
      (groupNumber >= 70 && groupNumber <= 88) ||
      (groupNumber >= 90 && groupNumber <= 92) ||
      (groupNumber >= 94 && groupNumber <= 99)
    } else {
      false
    }
  }

  /**
   * Check if an SSN is randomized based on issuance year
   */
  private def isRandomizedSSN(ssn: String): Boolean = {
    val ssnDigits = ssn.replaceAll("\\D", "")
    if (ssnDigits.length == 9) {
      SSNUtil.getSSNIssued(Some(ssnDigits), useV2 = true)
        .flatMap(parseYear)
        .exists(_ >= 2011)
    } else {
      false
    }
  }

  def isNationalIdRandomlyAssigned(resolvedNationalId: NationalId): Boolean = {
    val ssnYearLow = SSNUtil.ssnLookup(Some(resolvedNationalId.value), useV2 = true)
      .map(_.ssnYearLow)
      .orElse(SSNUtil.ssnLookup(Some(resolvedNationalId.value))
        .map(_.ssnYearLow))
      .getOrElse("0")
    if (ssnYearLow.toInt >= 2011) true
    else isRandomlyIssuedNationalIdHelper(resolvedNationalId)
  }

  /**
   * Get SSN issuance year range (low and high)
   */
  private def getSSNIssuanceYearRange(ssn: String, ssnIssued: Option[String], ssnYearLow: Option[String], ssnYearHigh: Option[String]): (Option[Int], Option[Int]) = {
    val lowYear = ssnYearLow.flatMap(parseYear)
      .orElse(SSNUtil.ssnLookup(Some(ssn.replaceAll("\\D", "")), useV2 = true).map(_.ssnYearLow).flatMap(parseYear))

    val highYear = ssnYearHigh.flatMap(parseYear)
      .orElse(SSNUtil.ssnLookup(Some(ssn.replaceAll("\\D", "")), useV2 = true).map(_.ssnYearHigh).flatMap(parseYear))

    // Fallback to ssnIssued if range is not available
    val fallbackYear = ssnIssued.flatMap(parseYear)
      .orElse(SSNUtil.getSSNIssued(Some(ssn.replaceAll("\\D", "")), useV2 = true).flatMap(parseYear))

    (lowYear.orElse(fallbackYear), highYear.orElse(fallbackYear))
  }

  /**
   * Check if input SSN matches the given SSN (handles 4-digit input)
   */
  private def matchesInputSSN(inputSSN: String, candidateSSN: String): Boolean = {
    val inputDigits = inputSSN.replaceAll("\\D", "")
    val candidateDigits = candidateSSN.replaceAll("\\D", "")

    if (inputDigits.length == 4) {
      candidateDigits.endsWith(inputDigits)
    } else {
      inputDigits == candidateDigits
    }
  }

  /**
   * Select the best DOB based on SSN issuance year range (SSNLowYear to SSNHighYear)
   * with additional logic for +1 year tolerance
   */
  private def selectBestDOB(dobs: Array[String], ssnLowYear: Option[Int], ssnHighYear: Option[Int]): Array[String] = {
    val dobsWithYears = dobs.flatMap { dob =>
      parseYear(dob).map(year => (dob, year))
    }

    if (dobsWithYears.isEmpty) return dobs

    (ssnLowYear, ssnHighYear) match {
      case (Some(lowYear), Some(highYear)) =>
        // First priority: DOBs within the exact SSN issuance year range
        val dobsWithinExactRange = dobsWithYears.filter { case (_, dobYear) =>
          dobYear >= lowYear && dobYear <= highYear
        }

        if (dobsWithinExactRange.nonEmpty) {
          // Return the DOB closest to the middle of the range
          val midRange = (lowYear + highYear) / 2.0
          val bestDOB = dobsWithinExactRange.minBy { case (_, dobYear) =>
            math.abs(dobYear - midRange)
          }
          Array(bestDOB._1)
        } else {
          // Second priority: DOBs within +1 year of the SSN issuance range
          val dobsWithinExtendedRange = dobsWithYears.filter { case (_, dobYear) =>
            dobYear >= (lowYear - 1) && dobYear <= (highYear + 1)
          }

          if (dobsWithinExtendedRange.nonEmpty) {
            // Return the DOB closest to the original range
            val bestDOB = dobsWithinExtendedRange.minBy { case (_, dobYear) =>
              if (dobYear < lowYear) {
                lowYear - dobYear  // Distance below range
              } else if (dobYear > highYear) {
                dobYear - highYear  // Distance above range
              } else {
                0  // Within original range (shouldn't happen as we checked above)
              }
            }
            Array(bestDOB._1)
          } else {
            // Third priority: If all DOBs are prior to the SSN issuance year, select the closest one
            val dobsPriorToRange = dobsWithYears.filter { case (_, dobYear) =>
              dobYear < lowYear
            }

            if (dobsPriorToRange.nonEmpty) {
              val bestDOB = dobsPriorToRange.maxBy { case (_, dobYear) => dobYear }
              Array(bestDOB._1)
            } else {
              // Last resort: Find DOB closest to the range
              val bestDOB = dobsWithYears.minBy { case (_, dobYear) =>
                if (dobYear < lowYear) {
                  lowYear - dobYear  // Distance below range
                } else {
                  dobYear - highYear  // Distance above range
                }
              }
              Array(bestDOB._1)
            }
          }
        }

      case (Some(lowYear), None) =>
        // Only low year available
        // First priority: DOBs at or after low year
        val dobsAtOrAfterLow = dobsWithYears.filter { case (_, dobYear) =>
          dobYear >= lowYear
        }

        if (dobsAtOrAfterLow.nonEmpty) {
          val bestDOB = dobsAtOrAfterLow.minBy { case (_, dobYear) => dobYear }
          Array(bestDOB._1)
        } else {
          // Second priority: DOBs within +1 year of low year (i.e., lowYear - 1)
          val dobsWithinTolerance = dobsWithYears.filter { case (_, dobYear) =>
            dobYear >= (lowYear - 1)
          }

          if (dobsWithinTolerance.nonEmpty) {
            val bestDOB = dobsWithinTolerance.maxBy { case (_, dobYear) => dobYear }
            Array(bestDOB._1)
          } else {
            // Third priority: All DOBs are prior to the tolerance, select the closest one
            val bestDOB = dobsWithYears.maxBy { case (_, dobYear) => dobYear }
            Array(bestDOB._1)
          }
        }

      case (None, Some(highYear)) =>
        // Only high year available
        // First priority: DOBs at or before high year
        val dobsAtOrBeforeHigh = dobsWithYears.filter { case (_, dobYear) =>
          dobYear <= highYear
        }

        if (dobsAtOrBeforeHigh.nonEmpty) {
          val bestDOB = dobsAtOrBeforeHigh.maxBy { case (_, dobYear) => dobYear }
          Array(bestDOB._1)
        } else {
          // Second priority: DOBs within +1 year of high year (i.e., highYear + 1)
          val dobsWithinTolerance = dobsWithYears.filter { case (_, dobYear) =>
            dobYear <= (highYear + 1)
          }

          if (dobsWithinTolerance.nonEmpty) {
            val bestDOB = dobsWithinTolerance.minBy { case (_, dobYear) => dobYear }
            Array(bestDOB._1)
          } else {
            // Third priority: All DOBs are after the tolerance, select the closest one
            val bestDOB = dobsWithYears.minBy { case (_, dobYear) => dobYear }
            Array(bestDOB._1)
          }
        }

      case (None, None) =>
        // No SSN year information available, return all DOBs unchanged
        dobs
    }
  }

  def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {
    val inputSSN = input.request.nationalId.map(_.value).getOrElse("")
    // Only process if there are multiple SSNs
    if (input.records.ssn.length > 1) {
      // Filter out ITINs from consideration
      val nonITINIndices = input.records.ssn.zipWithIndex.filter {
        case (ssn, _) => !isITIN(ssn)
      }.map(_._2)
      if (nonITINIndices.length > 1) {
        // Classify SSNs as randomized or non-randomized
        val ssnClassification = nonITINIndices.map { index =>
          val ssn = input.records.ssn(index)
          val ssnIssued = if (input.records.ssnIssued.length > index) Some(input.records.ssnIssued(index)) else None
          val ssnYearLow = if (input.records.ssnYearLow.length > index) Some(input.records.ssnYearLow(index)) else None
          val ssnYearHigh = if (input.records.ssnYearHigh.length > index) Some(input.records.ssnYearHigh(index)) else None
          val (lowYear, highYear) = getSSNIssuanceYearRange(ssn, ssnIssued, ssnYearLow, ssnYearHigh)
          val isRandomized = lowYear.exists(_ >= 2011) || isRandomizedSSN(ssn)
          val matchesInput = matchesInputSSN(inputSSN, ssn)

          (index, ssn, isRandomized, matchesInput, lowYear, highYear)
        }

        val randomizedSSNs = ssnClassification.filter(_._3)
        val nonRandomizedSSNs = ssnClassification.filter(!_._3)

        // Apply the exclusion logic
        val ssnIndicesToKeep = if (nonRandomizedSSNs.nonEmpty && randomizedSSNs.nonEmpty) {
          // Check if all SSNs are post-2011 (exception case)
          val allPost2011 = ssnClassification.forall { case (_, _, _, _, lowYearOpt, _) =>
            lowYearOpt.exists(_ >= 2011)
          }

          if (allPost2011) {
            // Keep all SSNs if all are post-2011
            nonITINIndices
          } else {
            // Keep non-randomized SSNs and any randomized SSNs that match the input
            val nonRandomizedIndices = nonRandomizedSSNs.map(_._1)
            val matchingRandomizedIndices = randomizedSSNs.filter(_._4).map(_._1)
            nonRandomizedIndices ++ matchingRandomizedIndices
          }
        } else {
          // Keep all non-ITIN SSNs if they're all randomized or all non-randomized
          nonITINIndices
        }

        // Add back ITIN indices
        val allIndicesToKeep = (ssnIndicesToKeep ++ input.records.ssn.zipWithIndex.filter {
          case (ssn, _) => isITIN(ssn)
        }.map(_._2)).sorted

        if (allIndicesToKeep.length != input.records.ssn.length) {
          val cleanedRecords = filterSSNFields(input.records, allIndicesToKeep.toArray)

          // Handle DOB selection based on remaining SSN issuance years
          val finalRecords = if (cleanedRecords.dob.length > 1 && cleanedRecords.ssn.nonEmpty) {
            // Get the primary SSN's issuance year range for DOB selection
            val primarySSN = cleanedRecords.ssn.head
            val primarySSNIssued = cleanedRecords.ssnIssued.headOption
            val primarySSNYearLow = cleanedRecords.ssnYearLow.headOption
            val primarySSNYearHigh = cleanedRecords.ssnYearHigh.headOption
            val (lowYear, highYear) = getSSNIssuanceYearRange(primarySSN, primarySSNIssued, primarySSNYearLow, primarySSNYearHigh)

            if (lowYear.isDefined || highYear.isDefined) {
              val selectedDOBs = selectBestDOB(cleanedRecords.dob, lowYear, highYear)
              val dobIndex = cleanedRecords.dob.indexOf(selectedDOBs.head)
              if (dobIndex >= 0) {
                cleanedRecords.copy(
                  dob = selectedDOBs,
                  piiRowIDs = cleanedRecords.piiRowIDs.copy(
                    dob = Array(cleanedRecords.piiRowIDs.dob(dobIndex))
                  )
                )
              } else {
                cleanedRecords
              }
            } else {
              cleanedRecords
            }
          } else {
            cleanedRecords
          }

          val cleanedIdentityRecords = input.identityRecord.map { identityRecord =>
            val (accepted, removed) = identityRecord.ssn.zipWithIndex.partition { case (_, index) =>
              allIndicesToKeep.contains(index)
            }
            identityRecord.copy(
              ssn = accepted.map(_._1),
              removed = identityRecord.removed.copy(ssn = identityRecord.removed.ssn ++ removed.map(_._1))
            )
          }

          RecordCleanupOperationResult(updated = true, finalRecords, cleanedIdentityRecords)
        } else {
          RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
        }
      } else {
        RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
      }
    } else {
      RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
    }
  }
}
