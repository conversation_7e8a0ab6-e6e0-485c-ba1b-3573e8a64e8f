package me.socure.common.kyc.model.es.result

import me.socure.common.kyc.model.NationalId
import me.socure.common.kyc.model.es.result.RecordsCleanupHelper._
import me.socure.common.kyc.model.es.result.SSNWeightComputer.isRandomlyIssuedNationalIdHelper
import me.socure.common.kyc.util.SSNUtil

/**
 * Cleanup operation to filter out randomized SSNs when non-randomized SSNs are also present.
 *
 * Logic:
 * 1. Exclude randomized SSNs if a non-randomized SSN is also present
 * 2. Exception: If SSN issuance year is on or after 2011 (both SSNs are randomized), do not exclude either SSN
 * 3. Ensure that the excluded SSN does not match the input SSN
 * 4. If input SSN is only 4 digits, match using the last four digits only
 *
 * Note: This logic does not apply to ITINs (Individual Taxpayer Identification Numbers).
 */
object RandomizedSSNCleanup extends RecordsCleanupOperation {
  val name = "RandomizedSSNCleanup"

  /**
   * Check if an SSN is an ITIN (Individual Taxpayer Identification Number)
   */
  private def isITIN(ssn: String): Boolean = {
    val ssnDigits = ssn.replaceAll("\\D", "")
    if (ssnDigits.length == 9 && ssnDigits.charAt(0) == '9') {
      val groupNumber = ssnDigits.substring(3, 5).toInt
      (groupNumber >= 50 && groupNumber <= 65) ||
      (groupNumber >= 70 && groupNumber <= 88) ||
      (groupNumber >= 90 && groupNumber <= 92) ||
      (groupNumber >= 94 && groupNumber <= 99)
    } else {
      false
    }
  }

  /**
   * Check if an SSN is randomized using the existing logic from NationalIDResolverHelper
   */
  private def isNationalIdRandomlyAssigned(resolvedNationalId: NationalId): Boolean = {
    val ssnYearLow = SSNUtil.ssnLookup(Some(resolvedNationalId.value), useV2 = true)
      .map(_.ssnYearLow)
      .orElse(SSNUtil.ssnLookup(Some(resolvedNationalId.value))
        .map(_.ssnYearLow))
      .getOrElse("0")
    if (ssnYearLow.toInt >= 2011) true
    else isRandomlyIssuedNationalIdHelper(resolvedNationalId)
  }

  /**
   * Get SSN issuance low year for 2011 check
   */
  private def getSSNIssuanceLowYear(ssn: String, ssnIssued: Option[String], ssnYearLow: Option[String]): Option[Int] = {
    ssnYearLow.flatMap(parseYear)
      .orElse(SSNUtil.ssnLookup(Some(ssn.replaceAll("\\D", "")), useV2 = true).map(_.ssnYearLow).flatMap(parseYear))
      .orElse(ssnIssued.flatMap(parseYear))
      .orElse(SSNUtil.getSSNIssued(Some(ssn.replaceAll("\\D", "")), useV2 = true).flatMap(parseYear))
  }

  /**
   * Check if input SSN matches the given SSN (handles 4-digit input)
   */
  private def matchesInputSSN(inputSSN: String, candidateSSN: String): Boolean = {
    val inputDigits = inputSSN.replaceAll("\\D", "")
    val candidateDigits = candidateSSN.replaceAll("\\D", "")

    if (inputDigits.length == 4) {
      candidateDigits.endsWith(inputDigits)
    } else {
      inputDigits == candidateDigits
    }
  }



  def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {
    val inputSSN = input.request.nationalId.map(_.value).getOrElse("")
    // Only process if there are multiple SSNs
    if (input.records.ssn.length > 1) {
      // Filter out ITINs from consideration
      val nonITINIndices = input.records.ssn.zipWithIndex.filter {
        case (ssn, _) => !isITIN(ssn)
      }.map(_._2)
      if (nonITINIndices.length > 1) {
        // Classify SSNs as randomized or non-randomized
        val ssnClassification = nonITINIndices.map { index =>
          val ssn = input.records.ssn(index)
          val ssnIssued = if (input.records.ssnIssued.length > index) Some(input.records.ssnIssued(index)) else None
          val ssnYearLow = if (input.records.ssnYearLow.length > index) Some(input.records.ssnYearLow(index)) else None
          val ssnYearHigh = if (input.records.ssnYearHigh.length > index) Some(input.records.ssnYearHigh(index)) else None
          val (lowYear, highYear) = getSSNIssuanceYearRange(ssn, ssnIssued, ssnYearLow, ssnYearHigh)
          val isRandomized = lowYear.exists(_ >= 2011) || isRandomizedSSN(ssn)
          val matchesInput = matchesInputSSN(inputSSN, ssn)

          (index, ssn, isRandomized, matchesInput, lowYear, highYear)
        }

        val randomizedSSNs = ssnClassification.filter(_._3)
        val nonRandomizedSSNs = ssnClassification.filter(!_._3)

        // Apply the exclusion logic
        val ssnIndicesToKeep = if (nonRandomizedSSNs.nonEmpty && randomizedSSNs.nonEmpty) {
          // Check if all SSNs are post-2011 (exception case)
          val allPost2011 = ssnClassification.forall { case (_, _, _, _, lowYearOpt, _) =>
            lowYearOpt.exists(_ >= 2011)
          }

          if (allPost2011) {
            // Keep all SSNs if all are post-2011
            nonITINIndices
          } else {
            // Keep non-randomized SSNs and any randomized SSNs that match the input
            val nonRandomizedIndices = nonRandomizedSSNs.map(_._1)
            val matchingRandomizedIndices = randomizedSSNs.filter(_._4).map(_._1)
            nonRandomizedIndices ++ matchingRandomizedIndices
          }
        } else {
          // Keep all non-ITIN SSNs if they're all randomized or all non-randomized
          nonITINIndices
        }

        // Add back ITIN indices
        val allIndicesToKeep = (ssnIndicesToKeep ++ input.records.ssn.zipWithIndex.filter {
          case (ssn, _) => isITIN(ssn)
        }.map(_._2)).sorted

        if (allIndicesToKeep.length != input.records.ssn.length) {
          val cleanedRecords = filterSSNFields(input.records, allIndicesToKeep.toArray)

          // Handle DOB selection based on remaining SSN issuance years
          val finalRecords = if (cleanedRecords.dob.length > 1 && cleanedRecords.ssn.nonEmpty) {
            // Get the primary SSN's issuance year range for DOB selection
            val primarySSN = cleanedRecords.ssn.head
            val primarySSNIssued = cleanedRecords.ssnIssued.headOption
            val primarySSNYearLow = cleanedRecords.ssnYearLow.headOption
            val primarySSNYearHigh = cleanedRecords.ssnYearHigh.headOption
            val (lowYear, highYear) = getSSNIssuanceYearRange(primarySSN, primarySSNIssued, primarySSNYearLow, primarySSNYearHigh)

            if (lowYear.isDefined || highYear.isDefined) {
              val selectedDOBs = selectBestDOB(cleanedRecords.dob, lowYear, highYear)
              val dobIndex = cleanedRecords.dob.indexOf(selectedDOBs.head)
              if (dobIndex >= 0) {
                cleanedRecords.copy(
                  dob = selectedDOBs,
                  piiRowIDs = cleanedRecords.piiRowIDs.copy(
                    dob = Array(cleanedRecords.piiRowIDs.dob(dobIndex))
                  )
                )
              } else {
                cleanedRecords
              }
            } else {
              cleanedRecords
            }
          } else {
            cleanedRecords
          }

          val cleanedIdentityRecords = input.identityRecord.map { identityRecord =>
            val (accepted, removed) = identityRecord.ssn.zipWithIndex.partition { case (_, index) =>
              allIndicesToKeep.contains(index)
            }
            identityRecord.copy(
              ssn = accepted.map(_._1),
              removed = identityRecord.removed.copy(ssn = identityRecord.removed.ssn ++ removed.map(_._1))
            )
          }

          RecordCleanupOperationResult(updated = true, finalRecords, cleanedIdentityRecords)
        } else {
          RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
        }
      } else {
        RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
      }
    } else {
      RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
    }
  }
}
