package me.socure.common.kyc.model.es.result

import me.socure.common.kyc.model.{City, CustomKYCPreferences, DOB, DataSources, FirstName, KYCPreferences, KycEntitySearchRequest, MiddleName, MobileNumber, NationalId, State, StreetAddress, SurName, Workflows, ZipCode}
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization
import org.scalatest.{FreeSpec, Matchers}

import scala.util.control.NonFatal

class RecordsCleanupOperationsTest extends FreeSpec with Matchers {

  private val sampleRecords = Records(
    firstName = Array("<PERSON>", "<PERSON>"),
    middleName = Array("A.", "B."),
    surName = Array("Doe", "Smith"),
    ssn = Array("123456789", "987654321"),
    dob = Array("19800101", "19900515"),
    mobileNumber = Array("5551234567", "5557654321"),
    streetAddress = Array("123 Elm St", "123456789456"),
    city = Array("Anytown", "Sometown"),
    zipCode = Array("12345", "54321"),
    state = Array("CA", "NY"),
    ssnDeceased = Array("0", "1"),
    cidDeceased = Array("1", "0"),
    invalidSSN = Array("1", "0"),
    ssnYearHigh = Array("2000", "2010"),
    ssnYearLow = Array("1980", "1990"),
    ssnConfirm = Array("1", "1"),
    factaCode = Array.empty,
    ciRowId = Array("CID123", "CID456"),
    aRowId = Array("AID789", "AID101"),
    addressType = Array("Residential", "Commercial"),
    rowIds = Array("RID1", "RID2"),
    clusterId = Some("Cluster123"),
    suffixName = Array("Jr.", "III"),
    phoneFirstSeen = Array("20000101", "20050101"),
    phoneLastSeen = Array("20200101", "20201231"),
    addressFirstSeen = Array("20000101", "20050101"),
    deceasedDate = Array.empty,
    piiRowIDs = PIIRowIDs(
      firstName = Array("PII123", "PII456"),
      surName = Array("PII123", "PII456"),
      mobileNumber = Array("PII123", "PII456"),
      dob = Array("PII123", "PII456")
    ),
    emailAddress = Array("<EMAIL>", "<EMAIL>"),
    emailFirstSeen = Array("20100101", "20120101"),
    emailLastSeen = Array("20200101", "20210101"),
    ssnIssued = Array("1980", "1990")
  )
  private val emptySampleRecords = Records(
    firstName = Array.empty[String],
    middleName = Array.empty[String],
    surName = Array.empty[String],
    ssn = Array.empty[String],
    dob = Array.empty[String],
    mobileNumber = Array.empty[String],
    streetAddress = Array.empty[String],
    city = Array.empty[String],
    zipCode = Array.empty[String],
    state = Array.empty[String],
    ssnDeceased = Array.empty[String],
    cidDeceased = Array.empty[String],
    invalidSSN = Array.empty[String],
    ssnYearHigh = Array.empty[String],
    ssnYearLow = Array.empty[String],
    ssnConfirm = Array.empty[String],
    factaCode = Array.empty,
    ciRowId = Array.empty[String],
    aRowId = Array.empty[String],
    addressType = Array.empty[String],
    rowIds = Array.empty[String],
    clusterId = None,
    suffixName = Array.empty[String],
    phoneFirstSeen = Array.empty[String],
    phoneLastSeen = Array.empty[String],
    addressFirstSeen = Array.empty[String],
    deceasedDate = Array.empty,
    piiRowIDs = PIIRowIDs(
      firstName = Array.empty[String],
      surName = Array.empty[String],
      mobileNumber = Array.empty[String],
      dob = Array.empty[String]
    ),
    emailAddress = Array.empty[String],
    emailFirstSeen = Array.empty[String],
    emailLastSeen = Array.empty[String],
    ssnIssued = Array.empty[String]
  )

  private val request = KycEntitySearchRequest(
    firstName = FirstName("ashley"),
    middleName = Some(MiddleName("N")),
    surName = SurName("paul"),
    streetAddress = Some(StreetAddress("602 ne bush prairie private se unit 12345")),
    city = Some(City("youngville")),
    zipCode = Some(ZipCode("72100")),
    zip4 = None,
    latitude = None,
    longitude = None,
    state = Some(State("la")),
    mobileNumber = Some(MobileNumber("**********")),
    nationalId = Some(NationalId("796040440")),
    dob = Some(DOB("20030208")),
    driverLicense = None,
    preferencesKyc = KYCPreferences(
      exactDob = true,
      exactSSN = true,
      dobMatchLogic = Some("exact_yyyy_mm_dd")
    ),
    customPreferencesKyc = CustomKYCPreferences(
      customDobMatchLogic = None,
      maxAddressCount = None,
      customNameMatching = None,
      maxEmailCount = None,
      maxPhoneCount = None
    ),
    preferencesEntity = KYCPreferences(
      exactDob = true,
      exactSSN = true,
      dobMatchLogic = Some("exact_yyyy_mm_dd")
    ),
    maskPii = false,
    workflows = Workflows.All,
    modulesEnabled = Set("KYC", "ModuleDecisioning")
  )

  private val sampleIdentityRecord = convertToIdentityRecord(sampleRecords)

  implicit val formats: DefaultFormats.type = DefaultFormats

  private def convertToIdentityRecord(record: Records): IdentityRecord = {

    def asOption[T](r: => T): Option[T] = {
      try {
        Some(r)
      } catch {
        case NonFatal(_) => None
      }
    }

    IdentityRecord(
      firstName = record.firstName.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.firstName(index))
        )
      },
      surName = record.surName.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.surName(index))
        )
      },
      middleName = record.middleName.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.surName(index))
        )
      },
      suffixName = record.suffixName.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.surName(index))
        )
      },
      dob = record.dob.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.dob(index))
        )
      },
      phoneNumber = record.mobileNumber.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.mobileNumber(index)),
          firstSeen = asOption(record.phoneFirstSeen(index)),
          lastSeen = asOption(record.phoneFirstSeen(index))
        )
      },
      email = record.emailAddress.zipWithIndex.map {
        case (value, index) => PIIField(
          value = value,
          rowId = asOption(record.piiRowIDs.mobileNumber(index)),
          firstSeen = asOption(record.emailFirstSeen(index)),
          lastSeen = asOption(record.emailLastSeen(index))
        )
      },
      ssn = record.ssn.zipWithIndex.map {
        case (value, index) => SSNField(
          value = value,
          rowId = asOption(record.ciRowId(index)),
          ssnConfirm = asOption(record.ssnConfirm(index)),
          invalid = asOption(record.invalidSSN(index)),
          ssnYearHigh = asOption(record.ssnYearHigh(index)),
          cidDeceased = asOption(record.cidDeceased(index)),
          ssnYearLow = asOption(record.ssnIssued(index)),
          ssnDeceased = asOption(record.ssnDeceased(index)),
          factaCode = asOption(record.factaCode(index)),
          dod = asOption(record.deceasedDate(index))
        )
      },
      address = record.streetAddress.zipWithIndex.map {
        case (street, index) => AddressField(
          street = street,
          city = record.city(index),
          state = record.state(index),
          zipCode = record.zipCode(index),
          rowId = asOption(record.aRowId(index)),
          firstSeen = asOption(record.addressFirstSeen(index)),
          lastSeen = asOption(record.addressFirstSeen(index)),
          addressCommercial = asOption(record.addressType(index))
        )
      },
      clusterId = record.clusterId.toSeq,
      allAssociatedSSNs = record.ssn.zipWithIndex.map {
        case (value, index) => SSNField(
          value = value,
          rowId = asOption(record.ciRowId(index)),
          ssnConfirm = asOption(record.ssnConfirm(index)),
          invalid = asOption(record.invalidSSN(index)),
          ssnYearHigh = asOption(record.ssnYearHigh(index)),
          cidDeceased = asOption(record.cidDeceased(index)),
          ssnYearLow = asOption(record.ssnIssued(index)),
          ssnDeceased = asOption(record.ssnDeceased(index)),
          factaCode = asOption(record.factaCode(index)),
          dod = asOption(record.deceasedDate(index))
        )
      }
    )
  }

  "multipleSSNSingleDOB cases" - {
    "case 1" in {
      val records = sampleRecords.copy(
        dob = Array("19900515"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII456"))
      )
      val expected = records.copy(
        ssn = Array("987654321"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2010"),
        ssnYearLow = Array("1990"),
        ssnIssued = Array("1990"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID456")
      )
      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("123456789")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(ssn = Seq(removedSSN)), allAssociatedSSNs = identityRecord.allAssociatedSSNs)
      val actual = MultipleSSNSingleDOB(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 2" in {
      val records = sampleRecords
      val expected = sampleRecords
      val expectedIdentityRecord = sampleIdentityRecord
      val actual = MultipleSSNSingleDOB(RecordCleanupOperationInput(request, records, Some(sampleIdentityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 3" in {
      val records = sampleRecords.copy(
        dob = Array("19900515"),
        ssnIssued = Array("1989", "1990"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII456"))
      )
      val identityRecord = Some(convertToIdentityRecord(records))
      val expected = records
      val actual = MultipleSSNSingleDOB(RecordCleanupOperationInput(request, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord shouldBe identityRecord
    }

    "case 4" in {
      val records = sampleRecords.copy(
        dob = Array("19900515"),
        ssnIssued = Array(),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII456"))
      )
      val identityRecord = Some(convertToIdentityRecord(records))
      val expected = records
      val actual = MultipleSSNSingleDOB(RecordCleanupOperationInput(request, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord shouldBe identityRecord
    }

    "case 5 - ssnIssued from file" in {
      val records = sampleRecords.copy(
        dob = Array("19400515"),
        ssn = Array("401401234", "757687030"),
        ssnIssued = Array("", "1930"),
        ssnConfirm = Array("1", "0"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("1950", "1980"),
        ssnYearLow = Array("1950", "1980"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("1", "1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID456", "CID457"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII456", "PII457"))
      )
      val expected = records.copy(
        ssn = Array("401401234"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("1950"),
        ssnYearLow = Array("1950"),
        ssnIssued = Array(""),
        cidDeceased = Array("0"),
        ssnDeceased = Array("1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID456")
      )
      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("757687030")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(ssn = Seq(removedSSN)), allAssociatedSSNs = identityRecord.allAssociatedSSNs)
      val actual = MultipleSSNSingleDOB(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 6 - ssnIssued from file" in {
      val records = sampleRecords.copy(
        dob = Array("19600515"),
        ssn = Array("401401234", "757687030"),
        ssnIssued = Array("", "1970"),
        ssnConfirm = Array("1", "0"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("1950", "1980"),
        ssnYearLow = Array("1950", "1970"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("1", "1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID456", "CID457"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII456", "PII457"))
      )
      val expected = records.copy(
        ssn = Array("757687030"),
        ssnConfirm = Array("0"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("1980"),
        ssnYearLow = Array("1970"),
        ssnIssued = Array("1970"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID457")
      )
      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("401401234")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(ssn = Seq(removedSSN)), allAssociatedSSNs = identityRecord.allAssociatedSSNs)
      val actual = MultipleSSNSingleDOB(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
  }

  "Invalid Street Address cases" - {
    "case 1" in {
      val records = sampleRecords.copy()
      val expected = records.copy(
        streetAddress = Array("123 Elm St"),
        city = Array("Anytown"),
        zipCode = Array("12345"),
        state = Array("CA"),
        aRowId = Array("AID789"),
        addressType = Array("Residential"),
        addressFirstSeen = Array("20000101")
      )
      val identityRecord = convertToIdentityRecord(records)
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved())
      val actual = InvalidStreetAddressCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get.address shouldBe expectedIdentityRecord.address
    }
  }

  "invalidSSNCleanup cases" - {
    "case 1" in {
      val records = sampleRecords.copy(
        dob = Array("19620515"),
        ssn = Array("532086789", "535426789")
      )
      val expected = records.copy(
        ssn = Array("535426789"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2010"),
        ssnYearLow = Array("1990"),
        ssnIssued = Array("1990"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID456")
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("532086789")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(ssn = Seq(removedSSN)), allAssociatedSSNs = identityRecord.allAssociatedSSNs)

      val actual = InvalidSSNCleanup(RecordCleanupOperationInput(request, records, Some(convertToIdentityRecord(records))))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 2" in {
      val records = sampleRecords
      val expected = sampleRecords
      val expectedIdentityRecord = sampleIdentityRecord
      val actual = InvalidSSNCleanup(RecordCleanupOperationInput(request, records, Some(expectedIdentityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 3" in {
      val records = sampleRecords.copy(
        dob = Array("19620515"),
        ssn = Array("532086789", "987654321")
      )
      val identityRecord = Some(convertToIdentityRecord(records))
      val expected = records
      val actual = InvalidSSNCleanup(RecordCleanupOperationInput(request, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord shouldBe identityRecord
    }

    "case 4" in {
      val records = sampleRecords.copy(
        dob = Array("19650515"),
        ssn = Array("532086789", "535426789")
      )
      val identityRecord = Some(convertToIdentityRecord(records))
      val expected = records
      val actual = InvalidSSNCleanup(RecordCleanupOperationInput(request, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord shouldBe identityRecord
    }
  }

  "multipleDOBSingleSSN cases" - {
    "case 1" in {
      val records = sampleRecords.copy(
        ssn = Array("123456789"),
        ssnIssued = Array("19820505")
      )
      val expected = records.copy(
        dob = Array("19800101"),
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII123"))
      )
      val identityRecord = convertToIdentityRecord(records)
      val removedDOB = identityRecord.dob.find(_.value.equals("19900515")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(dob = Seq(removedDOB)))
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 1.2" in {
      val records = sampleRecords.copy(
        ssn = Array("123456789"),
        ssnIssued = Array("05051982")
      )
      val expected = records.copy(
        dob = Array("19800101"),
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII123"))
      )
      val identityRecord = convertToIdentityRecord(records)
      val removedDOB = identityRecord.dob.find(_.value.equals("19900515")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(dob = Seq(removedDOB)))
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 2" in {
      val records = sampleRecords
      val expected = sampleRecords
      val expectedIdentityRecord = sampleIdentityRecord
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, Some(sampleIdentityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 3" in {
      val records = sampleRecords.copy(
        ssn = Array("123456789"),
        ssnIssued = Array("1989")
      )
      val identityRecord = Some(convertToIdentityRecord(records))
      val expected = records
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord shouldBe identityRecord
    }

    "case 4" in {
      val records = sampleRecords.copy(
        dob = Array("19900515"),
        ssnIssued = Array(),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII456"))
      )
      val identityRecord = Some(convertToIdentityRecord(records))
      val expected = records
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord shouldBe identityRecord
    }

    "case 5 - ssnIssued from file" in {
      val records = sampleRecords.copy(
        dob = Array("19900515", "19800515"),
        ssn = Array("421151234"),
        ssnIssued = Array(),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("PII456", "PII457"))
      )
      val expected = records.copy(
        dob = Array("19800515"),
        ssn = Array("421151234"),
        ssnIssued = Array(),
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII457"))
      )
      val identityRecord = convertToIdentityRecord(records)
      val removedDOB = identityRecord.dob.find(_.value.equals("19900515")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(dob = Seq(removedDOB)))
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 6" in {
      val records = sampleRecords.copy(
        dob = Array("19900515", "19900515"),
        ssn = Array("123456789"),
        ssnIssued = Array("19820505")
      )
      val expected = records
      val identityRecord = convertToIdentityRecord(records)
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(dob = Seq.empty))
      val actual = new MultipleDOBSingleSSN(true)(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      actual.updated shouldBe false
    }
  }

  "PrefillSSNBasedOnWeight cases" - {
    "case 1 should give precendence to ECBSV" in {
      val records = sampleRecords.copy(
        ssn = Array("000006789"),
        ssnIssued = Array("19820505")
      )
      val expected = records.copy(
        dob = Array("19800101"),
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII123"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val (ecbsvSSN: SSNField, correctedSSN: SSNField, confirmedSSN: SSNField, equifaxRandomised: SSNField, enformiamRandomised: SSNField) = fetchSSN(identityRecord)
      val ssn = Seq(ecbsvSSN, correctedSSN, confirmedSSN, equifaxRandomised, enformiamRandomised)

      val removedDOB = identityRecord.dob.find(_.value.equals("19900515")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(dob = Seq(removedDOB)))
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      val actual1 = PrefillSSNBasedOnWeight(RecordCleanupOperationInput(request, actual.records, Some(actual.identityRecord.get.copy(allAssociatedSSNs = ssn))))
      actual1.records.ssn shouldBe Array("912346789")
    }


    "case 1a  should not give precendence to ECBSV if its improper" in {
      val records = sampleRecords.copy(
        ssn = Array("000006789"),
        ssnIssued = Array("19820505")
      )
      val expected = records.copy(
        dob = Array("19800101"),
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII123"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val (ecbsvSSN: SSNField, correctedSSN: SSNField, confirmedSSN: SSNField, equifaxRandomised: SSNField, enformiamRandomised: SSNField) = fetchSSN(identityRecord)
      val ssn = Seq(ecbsvSSN.copy(value = "000006789"), correctedSSN, confirmedSSN, equifaxRandomised, enformiamRandomised)

      val removedDOB = identityRecord.dob.find(_.value.equals("19900515")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(dob = Seq(removedDOB)))
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      val actual1 = PrefillSSNBasedOnWeight(RecordCleanupOperationInput(request, actual.records, Some(actual.identityRecord.get.copy(allAssociatedSSNs = ssn))))
      actual1.records.ssn shouldBe Array("812346789")
    }

    "case 2 should give precendence to corrected" in {
      val records = sampleRecords.copy(
        ssn = Array("000006789"),
        ssnIssued = Array("19820505")
      )
      val expected = records.copy(
        dob = Array("19800101"),
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII123"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val (ecbsvSSN: SSNField, correctedSSN: SSNField, confirmedSSN: SSNField, equifaxRandomised: SSNField, enformiamRandomised: SSNField) = fetchSSN(identityRecord)
      val ssn = Seq(correctedSSN, confirmedSSN, equifaxRandomised, enformiamRandomised)

      val removedDOB = identityRecord.dob.find(_.value.equals("19900515")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(dob = Seq(removedDOB)))
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      val actual1 = PrefillSSNBasedOnWeight(RecordCleanupOperationInput(request, actual.records, Some(actual.identityRecord.get.copy(allAssociatedSSNs = ssn))))
      actual1.records.ssn shouldBe Array("812346789")
    }

    "case 3 should give precendence to confirmed" in {
      val records = sampleRecords.copy(
        ssn = Array("000006789"),
        ssnIssued = Array("19820505")
      )
      val expected = records.copy(
        dob = Array("19800101"),
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII123"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val (ecbsvSSN: SSNField, correctedSSN: SSNField, confirmedSSN: SSNField, equifaxRandomised: SSNField, enformiamRandomised: SSNField) = fetchSSN(identityRecord)
      val ssn = Seq(confirmedSSN, equifaxRandomised, enformiamRandomised)

      val removedDOB = identityRecord.dob.find(_.value.equals("19900515")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(dob = Seq(removedDOB)))
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      val actual1 = PrefillSSNBasedOnWeight(RecordCleanupOperationInput(request, actual.records, Some(actual.identityRecord.get.copy(allAssociatedSSNs = ssn))))
      actual1.records.ssn shouldBe Array("712346789")
    }

    "case 4 should give precendence to EQUIFAX RANDOMISED" in {
      val records = sampleRecords.copy(
        ssn = Array("000006789"),
        ssnIssued = Array("19820505")
      )
      val expected = records.copy(
        dob = Array("19800101"),
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII123"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val (ecbsvSSN: SSNField, correctedSSN: SSNField, confirmedSSN: SSNField, equifaxRandomised: SSNField, enformiamRandomised: SSNField) = fetchSSN(identityRecord)
      val ssn = Seq(equifaxRandomised, enformiamRandomised)

      val removedDOB = identityRecord.dob.find(_.value.equals("19900515")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(dob = Seq(removedDOB)))
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      val actual1 = PrefillSSNBasedOnWeight(RecordCleanupOperationInput(request, actual.records, Some(actual.identityRecord.get.copy(allAssociatedSSNs = ssn))))
      actual1.records.ssn shouldBe Array("528936789")
    }

    "case 5 should give precendence to ENFORMIAM" in {
      val records = sampleRecords.copy(
        ssn = Array("000006789"),
        ssnIssued = Array("19820505")
      )
      val expected = records.copy(
        dob = Array("19800101"),
        piiRowIDs = records.piiRowIDs.copy(dob = Array("PII123"))
      )

      val identityRecord = convertToIdentityRecord(records)
      val (ecbsvSSN: SSNField, correctedSSN: SSNField, confirmedSSN: SSNField, equifaxRandomised: SSNField, enformiamRandomised: SSNField) = fetchSSN(identityRecord)
      val ssn = Seq(enformiamRandomised)

      val removedDOB = identityRecord.dob.find(_.value.equals("19900515")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(dob = Seq(removedDOB)))
      val actual = new MultipleDOBSingleSSN(false)(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      val actual1 = PrefillSSNBasedOnWeight(RecordCleanupOperationInput(request, actual.records, Some(actual.identityRecord.get.copy(allAssociatedSSNs = ssn))))
      actual1.records.ssn shouldBe Array("428936789")
    }
  }

  private def fetchSSN(identityRecord: IdentityRecord) = {
    val ecbsvSSN = identityRecord.allAssociatedSSNs.head.copy(value = "912346789", source = Some(DataSources.Ecbsv))
    val correctedSSN = identityRecord.allAssociatedSSNs.head.copy(value = "812346789", source = Some(DataSources.Enformion), isCorrected = true)
    val confirmedSSN = identityRecord.allAssociatedSSNs.head.copy(value = "712346789", source = Some(DataSources.Equifax), ssnConfirm = Some("1"))
    val equifaxRandomised = identityRecord.allAssociatedSSNs.head.copy(value = "528936789", source = Some(DataSources.Equifax))
    val enformiamRandomised = identityRecord.allAssociatedSSNs.head.copy(value = "428936789", source = Some(DataSources.Enformion))
    (ecbsvSSN, correctedSSN, confirmedSSN, equifaxRandomised, enformiamRandomised)
  }

  "PlaceHolderNamesCleanUp Cases" - {
    "case 1" in {
      // if no Placeholder names exists, the records should remain the same.
      val records = sampleRecords.copy(
        firstName = Array("Nina", "Katy", "Taylor", "Justin", "Edward"),
        surName = Array("Nesbitt", "Perry", "Swift", "Bieber", "Sheeran")
      )
      val identityRecord = convertToIdentityRecord(records)
      val actual = PlaceHolderNamesCleanUp(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
    }
    "case 2" in {
      // empty records should fail / return random values
      val records = sampleRecords.copy(
        firstName = Array(),
        surName = Array()
      )
      val identityRecord = convertToIdentityRecord(records)
      val actual = PlaceHolderNamesCleanUp(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
    }
    "case 3" in {
      // remove placeholder names when present
      val records = sampleRecords.copy(
        firstName = Array("John", "Katy", "Taylor", "Justin", "Edward", "Jane", "Jane", "John"),
        surName = Array("Doe", "Perry", "Swift", "Bieber", "Sheeran", "Doe", "Sitwell", "Rooney"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(
          firstName = Array("PIIFN001", "PIIFN002", "PIIFN003", "PIIFN004", "PIIFN005", "PIIFN006", "PIIFN007", "PIIFN008"),
          surName = Array("PIISN001", "PIISN002", "PIISN003", "PIISN004", "PIISN005", "PIISN006", "PIISN007", "PIISN008")
        )
      )
      val removedFirstName = List(PIIField(value="John", rowId=Some("PIIFN001")), PIIField(value="Jane", rowId=Some("PIIFN006")))
      val removedSurName = List(PIIField(value="Doe", rowId=Some("PIISN001")), PIIField(value="Doe", rowId=Some("PIISN006")))
      val identityRecord = convertToIdentityRecord(records)
      val expectedRecords = sampleRecords.copy(
        firstName = Array("Katy", "Taylor", "Justin", "Edward", "Jane", "John"),
        surName = Array("Perry", "Swift", "Bieber", "Sheeran", "Sitwell", "Rooney"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(
          firstName = Array("PIIFN002", "PIIFN003", "PIIFN004", "PIIFN005", "PIIFN007", "PIIFN008"),
          surName = Array("PIISN002", "PIISN003", "PIISN004", "PIISN005", "PIISN007", "PIISN008")
        )
      )
      var expectedIdentityRecords = convertToIdentityRecord(expectedRecords)
      expectedIdentityRecords = expectedIdentityRecords.copy(
        removed = expectedIdentityRecords.removed.append(IdentityRecordRemoved(firstName = removedFirstName)).append(IdentityRecordRemoved(surName = removedSurName))
      )
      val actual = PlaceHolderNamesCleanUp(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expectedRecords)
      actual.identityRecord.get.firstName shouldBe expectedIdentityRecords.firstName
      actual.identityRecord.get.surName shouldBe expectedIdentityRecords.surName
      actual.identityRecord.get.removed shouldBe expectedIdentityRecords.removed
    }

  }

  "removeNonCHMSSN cases" - {
    "case 1" in {
      val records = sampleRecords.copy(
        ciRowId = Array("CHM123", "CID456")
      )
      val expected = records.copy(
        ssn = Array("123456789"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("1"),
        ssnYearHigh = Array("2000"),
        ssnYearLow = Array("1980"),
        ssnIssued = Array("1980"),
        cidDeceased = Array("1"),
        ssnDeceased = Array("0"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CHM123")
      )
      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("987654321")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(ssn = Seq(removedSSN)), allAssociatedSSNs = identityRecord.allAssociatedSSNs)
      val actual = RemoveNonCHMSSN(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 2" in {
      val records = sampleRecords
      val expected = sampleRecords
      val expectedIdentityRecord = sampleIdentityRecord
      val actual = RemoveNonCHMSSN(RecordCleanupOperationInput(request, records, Some(sampleIdentityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 3" in {
      val records = sampleRecords.copy(
        ciRowId = Array("CHM123", "CHM456")
      )
      val identityRecord = Some(convertToIdentityRecord(records))
      val expected = records
      val actual = RemoveNonCHMSSN(RecordCleanupOperationInput(request, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord shouldBe identityRecord
    }

    "case 4" in {
      val records = sampleRecords.copy(
        ciRowId = Array("CHM123", "CID456"),
        ssn = Array("123456789", "796040440")
      )
      val expected = records
      val identityRecord = Some(convertToIdentityRecord(records))
      val actual = RemoveNonCHMSSN(RecordCleanupOperationInput(request, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord shouldBe identityRecord
    }

    "case 5" in {
      val requestUpdated = request.copy(nationalId = Some(NationalId("7899")))
      val records = sampleRecords.copy(
        ciRowId = Array("CHM123", "CID456"),
        ssn = Array("123456789", "796047899")
      )
      val expected = records
      val identityRecord = Some(convertToIdentityRecord(records))
      val actual = RemoveNonCHMSSN(RecordCleanupOperationInput(requestUpdated, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord shouldBe identityRecord
    }
  }

  "RequestBasedNameCleanup cases" - {
    val firstName = Array("david", "davidc", "david c", "david curtis")
    val surName = Array("flemmingjr", "flemming jr", "flemming", "flemming junior")
    val records = sampleRecords.copy(
      firstName = firstName,
      surName = surName
    )
    val identityRecord = Some(convertToIdentityRecord(records))

    "case 1" in {
      val updatedRequest = request.copy(firstName = FirstName("davidc"), surName = SurName("flemming jr"))
      val expectedRecords = sampleRecords.copy(
        firstName = Array("david", "davidc", "david c", "david curtis"),
        surName = Array("flemming jr", "flemming jr", "flemming", "flemming junior")
      )
      val expectedIdentityRecords = Some(convertToIdentityRecord(expectedRecords))
      val afterCleanUp = RequestBasedNameCleanup(RecordCleanupOperationInput(updatedRequest, records, identityRecord))
      Serialization.write(afterCleanUp.records) shouldBe Serialization.write(expectedRecords)
      afterCleanUp.identityRecord shouldBe expectedIdentityRecords
    }

    "case 2" in {
      val updatedRequest = request.copy(firstName = FirstName("david c"), surName = SurName("flemmingjr"))
      val expectedRecords = sampleRecords.copy(
        firstName = Array("david", "david c", "david c", "david curtis"),
        surName = Array("flemmingjr", "flemming jr", "flemming", "flemming junior")
      )
      val expectedIdentityRecords = Some(convertToIdentityRecord(expectedRecords))
      val afterCleanUp = RequestBasedNameCleanup(RecordCleanupOperationInput(updatedRequest, records, identityRecord))
      Serialization.write(afterCleanUp.records) shouldBe Serialization.write(expectedRecords)
      afterCleanUp.identityRecord shouldBe expectedIdentityRecords
    }

    "case 3" in {
      val updatedRequest = request.copy(firstName = FirstName("david c"), surName = SurName("flemming jr"))
      val expectedRecords = sampleRecords.copy(
        firstName = Array("david", "david c", "david c", "david curtis"),
        surName = Array("flemming jr", "flemming jr", "flemming", "flemming junior")
      )
      val expectedIdentityRecords = Some(convertToIdentityRecord(expectedRecords))
      val afterCleanUp = RequestBasedNameCleanup(RecordCleanupOperationInput(updatedRequest, records, identityRecord))
      Serialization.write(afterCleanUp.records) shouldBe Serialization.write(expectedRecords)
      afterCleanUp.identityRecord shouldBe expectedIdentityRecords
    }

    "case 4" in {
      val updatedRequest = request.copy(firstName = FirstName("david"), surName = SurName("flemming"))
      val expectedRecords = sampleRecords.copy(
        firstName = Array("david", "davidc", "david c", "david curtis"),
        surName = Array("flemmingjr", "flemming jr", "flemming", "flemming junior")
      )
      val expectedIdentityRecords = Some(convertToIdentityRecord(expectedRecords))
      val afterCleanUp = RequestBasedNameCleanup(RecordCleanupOperationInput(updatedRequest, records, identityRecord))
      Serialization.write(afterCleanUp.records) shouldBe Serialization.write(expectedRecords)
      afterCleanUp.identityRecord shouldBe expectedIdentityRecords
    }

    "case 5" in {
      val updatedRequest = request.copy(firstName = FirstName("flemming jr"), surName = SurName("david c"))
      val expectedRecords = sampleRecords.copy(
        firstName = Array("david", "david c", "david c", "david curtis"),
        surName = Array("flemming jr", "flemming jr", "flemming", "flemming junior")
      )
      val expectedIdentityRecords = Some(convertToIdentityRecord(expectedRecords))
      val afterCleanUp = RequestBasedNameCleanup(RecordCleanupOperationInput(updatedRequest, records, identityRecord))
      Serialization.write(afterCleanUp.records) shouldBe Serialization.write(expectedRecords)
      afterCleanUp.identityRecord shouldBe expectedIdentityRecords
    }

    "case 6" in {
      val updatedRequest = request.copy(firstName = FirstName("flemming"), surName = SurName("david c"))
      val expectedRecords = sampleRecords.copy(
        firstName = Array("david", "david c", "david c", "david curtis"),
        surName = Array("flemmingjr", "flemming jr", "flemming", "flemming junior")
      )
      val expectedIdentityRecords = Some(convertToIdentityRecord(expectedRecords))
      val afterCleanUp = RequestBasedNameCleanup(RecordCleanupOperationInput(updatedRequest, records, identityRecord))
      Serialization.write(afterCleanUp.records) shouldBe Serialization.write(expectedRecords)
      afterCleanUp.identityRecord shouldBe expectedIdentityRecords
    }

    "case 7" in {
      val updatedRequest = request.copy(firstName = FirstName("flemming"), surName = SurName("david"))
      val expectedRecords = sampleRecords.copy(
        firstName = Array("david", "davidc", "david c", "david curtis"),
        surName = Array("flemmingjr", "flemming jr", "flemming", "flemming junior")
      )
      val expectedIdentityRecords = Some(convertToIdentityRecord(expectedRecords))
      val afterCleanUp = RequestBasedNameCleanup(RecordCleanupOperationInput(updatedRequest, records, identityRecord))
      Serialization.write(afterCleanUp.records) shouldBe Serialization.write(expectedRecords)
      afterCleanUp.identityRecord shouldBe expectedIdentityRecords
    }

    "case 8" in {
      val updatedRequest = request.copy(firstName = FirstName("flemming jr"), surName = SurName("david"))
      val expectedRecords = sampleRecords.copy(
        firstName = Array("david", "davidc", "david c", "david curtis"),
        surName = Array("flemming jr", "flemming jr", "flemming", "flemming junior")
      )
      val expectedIdentityRecords = Some(convertToIdentityRecord(expectedRecords))
      val afterCleanUp = RequestBasedNameCleanup(RecordCleanupOperationInput(updatedRequest, records, identityRecord))
      Serialization.write(afterCleanUp.records) shouldBe Serialization.write(expectedRecords)
      afterCleanUp.identityRecord shouldBe expectedIdentityRecords
    }
  }

  "SourcePrioritizationDobCleanUp cases" - {
    "case 1" in {
      val records = sampleRecords.copy(
        dob = Array("19821215", "19821221"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("DOCV", "CH"))
      )
      val expected = records.copy(
        dob = Array("19821215"),
        piiRowIDs = records.piiRowIDs.copy(dob = Array("DOCV"))
      )
      var identityRecord = convertToIdentityRecord(records)
      identityRecord = identityRecord.copy(
        dob = Seq(
          identityRecord.dob.head.copy(source = Some(DataSources.DocV)),
          identityRecord.dob.last.copy(source = Some(DataSources.Equifax))
        )
      )
      val removedDOB = identityRecord.dob.find(_.value.equals("19821221")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        dob = Seq(
          identityRecord.dob.head.copy(source = Some(DataSources.DocV))
        ),
        removed = IdentityRecordRemoved(dob = Seq(removedDOB))
      )
      val actual = SourcePrioritizationDobCleanUp(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 2" in {
      val records = sampleRecords.copy(
        dob = Array("19821221", "1980", "198212", "19821221", "19820121"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("CH", "CH", "CH", "BLD", "CH"))
      )
      val expected = records.copy(
        dob = Array("19821221", "19821221"),
        piiRowIDs = records.piiRowIDs.copy(dob = Array("CH", "BLD"))
      )
      var identityRecord = convertToIdentityRecord(records)
      identityRecord = identityRecord.copy(
        dob = Seq(
          identityRecord.dob.head.copy(source = Some(DataSources.Equifax)),
          identityRecord.dob(1).copy(source = Some(DataSources.Equifax)),
          identityRecord.dob(2).copy(source = Some(DataSources.Equifax)),
          identityRecord.dob(3).copy(source = Some(DataSources.Bold)),
          identityRecord.dob.last.copy(source = Some(DataSources.Enformion))
        )
      )
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        dob = Seq(
          identityRecord.dob.head,
          identityRecord.dob(3)
        ),
        removed = IdentityRecordRemoved(dob = Seq(
          identityRecord.dob(4),
          identityRecord.dob(1),
          identityRecord.dob(2)
        ))
      )
      val actual = FrequencyBasedDobCleanUp(RecordCleanupOperationInput(request.copy(dob=Some(DOB("19821221"))), records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 3" in {
      val records = sampleRecords.copy(
        dob = Array("19821221", "1980", "198212", "19821220", "19820121"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("CH", "CH", "CH", "BLD", "CH"))
      )
      val expected = records
      var identityRecord = convertToIdentityRecord(records)
      identityRecord = identityRecord.copy(
        dob = Seq(
          identityRecord.dob.head.copy(source = Some(DataSources.Equifax)),
          identityRecord.dob(1).copy(source = Some(DataSources.Equifax)),
          identityRecord.dob(2).copy(source = Some(DataSources.Equifax)),
          identityRecord.dob(3).copy(source = Some(DataSources.Bold)),
          identityRecord.dob.last.copy(source = Some(DataSources.Enformion))
        )
      )
      val expectedIdentityRecord = identityRecord
      val actual = SourcePrioritizationDobCleanUp(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }

    "case 4" in {
      val records = sampleRecords.copy(
        dob = Array("19821221", "1980", "198212", "19821221", "19820121"),
        piiRowIDs = sampleRecords.piiRowIDs.copy(dob = Array("CH", "CH", "CH", "BLD", "CH"))
      )
      val expected = records
      val identityRecord = convertToIdentityRecord(records)
      val actual = FrequencyBasedDobCleanUp(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe identityRecord
    }

  }

  "InitialFirstNameCleanUp TestCases" - {
    "case 1" in {
      // remove several initials for which longer names are present
      val fnArray = Array("Andrews", "Anne", "A", "A", "Andrew", "L", "Lithum", "M")
      val piiRowArray = Array("PIIFN001", "PIIFN002", "PIIFN003", "PIIFN004", "PIIFN005", "PIIFN006", "PIIFN007", "PIIFN008")
      val inclusionIdx = Array(0, 1, 4, 6, 7)
      val fnArrayExpected = inclusionIdx.map(fnArray)
      val piiRowExpected = inclusionIdx.map(piiRowArray)
      val records = sampleRecords.copy(
        firstName = fnArray,
        piiRowIDs = sampleRecords.piiRowIDs.copy(
          firstName = piiRowArray
        )
      )
      val expected = records.copy(
        firstName = fnArrayExpected,
        piiRowIDs = sampleRecords.piiRowIDs.copy(
          firstName = piiRowExpected
        )
      )
      val identityRecord = convertToIdentityRecord(records)
      var expectedIdentityRecord = convertToIdentityRecord(expected)
      expectedIdentityRecord = expectedIdentityRecord.copy(
        removed = expectedIdentityRecord.removed.append(
          IdentityRecordRemoved(
            firstName =
              List(
                PIIField(value = "A", rowId = Some("PIIFN003")),
                PIIField(value = "A", rowId = Some("PIIFN004")),
                PIIField(value = "L", rowId = Some("PIIFN006"))
              )
          )
        )
      )
      val actual = InitialFirstNameCleanUp(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      actual.records.firstName shouldBe expected.firstName
      actual.records.surName shouldBe expected.surName
      actual.identityRecord.get.firstName shouldBe expectedIdentityRecord.firstName
      actual.identityRecord.get.surName shouldBe expectedIdentityRecord.surName
      actual.identityRecord.get.removed shouldBe expectedIdentityRecord.removed
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
    "case 2" in {
      // remove several initials for which longer names are present - 2
      val fnArray = Array("Anne", "M", "A", "L", "B", "D")
      val piiRows = Array("PIIFN001", "PIIFN002", "PIIFN003", "PIIFN004", "PIIFN005", "PIIFN006")
      val inclusionIdx = Array(0, 1, 3, 4, 5)
      val fnArrayExpected = inclusionIdx.map(fnArray)
      val piiRowExpected = inclusionIdx.map(piiRows)
      val records = sampleRecords.copy(
        firstName = fnArray,
        piiRowIDs = sampleRecords.piiRowIDs.copy(
          firstName = piiRows
        )
      )
      val expected = records.copy(
        firstName = fnArrayExpected,
        piiRowIDs = sampleRecords.piiRowIDs.copy(
          firstName = piiRowExpected
        )
      )
      val identityRecord = convertToIdentityRecord(records)
      var expectedIdentityRecord = convertToIdentityRecord(expected)
      expectedIdentityRecord = expectedIdentityRecord.copy(
        removed = expectedIdentityRecord.removed.append(
          IdentityRecordRemoved(
            firstName =
              List(
                PIIField(value = "A", rowId = Some("PIIFN003"))
              )
          )
        )
      )
      val actual = InitialFirstNameCleanUp(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      actual.records.firstName shouldBe expected.firstName
      actual.records.surName shouldBe expected.surName
      actual.identityRecord.get.firstName shouldBe expectedIdentityRecord.firstName
      actual.identityRecord.get.surName shouldBe expectedIdentityRecord.surName
      actual.identityRecord.get.removed shouldBe expectedIdentityRecord.removed
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
    "case 3" in {
      // empty records, cleanup shouldn't fail/return values
      val fnArray = Array.empty[String]
      val fnArrayExpected = Array.empty[String]
      val records = sampleRecords.copy(firstName = fnArray)
      val expected = records.copy(
        firstName = fnArrayExpected
      )
      val identityRecord = convertToIdentityRecord(records)
      val expectedIdentityRecord = convertToIdentityRecord(expected)
      val actual = InitialFirstNameCleanUp(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      actual.records.firstName shouldBe expected.firstName
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
    "case 4" in {
      // all are initials, so input is left untouched
      val fnArray = Array("A", "B", "C", "D", "E", "A")
      val fnArrayExpected = Array("A", "B", "C", "D", "E", "A")
      val records = sampleRecords.copy(firstName = fnArray)
      val expected = records.copy(
        firstName = fnArrayExpected
      )
      val identityRecord = convertToIdentityRecord(records)
      val expectedIdentityRecord = convertToIdentityRecord(expected)
      val actual = InitialFirstNameCleanUp(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      actual.records.firstName shouldBe expected.firstName
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
    "case 5" in {
      // no initials present in input array, so nothing removed
      val fnArray = Array("Anne", "Frank", "Sebastian", "Shultz")
      val fnArrayExpected = Array("Anne", "Frank", "Sebastian", "Shultz")
      val records = sampleRecords.copy(firstName = fnArray)
      val expected = records.copy(
        firstName = fnArrayExpected
      )
      val identityRecord = convertToIdentityRecord(records)
      val expectedIdentityRecord = convertToIdentityRecord(expected)
      val actual = InitialFirstNameCleanUp(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      actual.records.firstName shouldBe expected.firstName
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
  }
  "SerializedSSNCleanup Tests" - {
    "case 1: should remove serialized SSNs (123456789) when non-serialized SSNs are present" in {
      val records = sampleRecords.copy(
        ssn = Array("123456789", "532081234"),
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2010"),
        ssnYearLow = Array("1980", "1990"),
        ssnIssued = Array("1980", "1990"),
        cidDeceased = Array("1", "0"),
        ssnDeceased = Array("0", "1")
      )

      val expected = records.copy(
        ssn = Array("532081234"),
        ciRowId = Array("CID456"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2010"),
        ssnYearLow = Array("1990"),
        ssnIssued = Array("1990"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("1")
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("123456789")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(ssn = Seq(removedSSN)),
        allAssociatedSSNs = identityRecord.allAssociatedSSNs
      )

      val actual = SerializedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      actual.updated shouldBe true
    }

    "case 2: should remove repeated digit SSNs (111111111) when non-serialized SSNs are present" in {
      val records = sampleRecords.copy(
        ssn = Array("111111111", "532081234"),
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2010"),
        ssnYearLow = Array("1980", "1990"),
        ssnIssued = Array("1980", "1990"),
        cidDeceased = Array("1", "0"),
        ssnDeceased = Array("0", "1")
      )

      val expected = records.copy(
        ssn = Array("532081234"),
        ciRowId = Array("CID456"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2010"),
        ssnYearLow = Array("1990"),
        ssnIssued = Array("1990"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("1")
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("111111111")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(ssn = Seq(removedSSN)),
        allAssociatedSSNs = identityRecord.allAssociatedSSNs
      )

      val actual = SerializedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      actual.updated shouldBe true
    }

    "case 3: should remove SSNs with pattern *********** when non-serialized SSNs are present" in {
      val records = sampleRecords.copy(
        ssn = Array("222554444", "532081234"),
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2010"),
        ssnYearLow = Array("1980", "1990"),
        ssnIssued = Array("1980", "1990"),
        cidDeceased = Array("1", "0"),
        ssnDeceased = Array("0", "1")
      )

      val expected = records.copy(
        ssn = Array("532081234"),
        ciRowId = Array("CID456"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2010"),
        ssnYearLow = Array("1990"),
        ssnIssued = Array("1990"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("1")
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("222554444")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(ssn = Seq(removedSSN)),
        allAssociatedSSNs = identityRecord.allAssociatedSSNs
      )

      val actual = SerializedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      actual.updated shouldBe true
    }

    "case 4: should remove SSNs from known_invalid_ssns.txt file when non-serialized SSNs are present" in {
      val records = sampleRecords.copy(
        ssn = Array("219099999", "532081234"), // 219099999 is from the known_invalid_ssns.txt file
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2010"),
        ssnYearLow = Array("1980", "1990"),
        ssnIssued = Array("1980", "1990"),
        cidDeceased = Array("1", "0"),
        ssnDeceased = Array("0", "1")
      )

      val expected = records.copy(
        ssn = Array("532081234"),
        ciRowId = Array("CID456"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2010"),
        ssnYearLow = Array("1990"),
        ssnIssued = Array("1990"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("1")
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("219099999")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(ssn = Seq(removedSSN)),
        allAssociatedSSNs = identityRecord.allAssociatedSSNs
      )

      val actual = SerializedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      actual.updated shouldBe true
    }

    "case 5: should remove SSNs with invalid area numbers (666123456) when non-serialized SSNs are present" in {
      val records = sampleRecords.copy(
        ssn = Array("666123456", "532081234"),
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2010"),
        ssnYearLow = Array("1980", "1990"),
        ssnIssued = Array("1980", "1990"),
        cidDeceased = Array("1", "0"),
        ssnDeceased = Array("0", "1")
      )

      val expected = records.copy(
        ssn = Array("532081234"),
        ciRowId = Array("CID456"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2010"),
        ssnYearLow = Array("1990"),
        ssnIssued = Array("1990"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("1")
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("666123456")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(ssn = Seq(removedSSN)),
        allAssociatedSSNs = identityRecord.allAssociatedSSNs
      )

      val actual = SerializedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      actual.updated shouldBe true
    }

    "case 4: should not remove SSNs if only serialized SSNs are present" in {
      val records = sampleRecords.copy(
        ssn = Array("123456789", "111111111"),
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2010"),
        ssnYearLow = Array("1980", "1990"),
        ssnIssued = Array("1980", "1990"),
        cidDeceased = Array("1", "0"),
        ssnDeceased = Array("0", "1")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = SerializedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
      actual.updated shouldBe false
    }

    "case 5: should not remove SSNs if only non-serialized SSNs are present" in {
      val records = sampleRecords.copy(
        ssn = Array("532081234", "563454721"),
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2010"),
        ssnYearLow = Array("1980", "1990"),
        ssnIssued = Array("1980", "1990"),
        cidDeceased = Array("1", "0"),
        ssnDeceased = Array("0", "1")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = SerializedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
      actual.updated shouldBe false
    }

    "case 6: should not modify records if only one SSN is present, even if serialized" in {
      val records = sampleRecords.copy(
        ssn = Array("123456789"),
        ciRowId = Array("CID123"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2000"),
        ssnYearLow = Array("1980"),
        ssnIssued = Array("1980"),
        cidDeceased = Array("1"),
        ssnDeceased = Array("0")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = SerializedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
      actual.updated shouldBe false
    }

    "case 7: should handle multiple serialized SSNs with one non-serialized SSN" in {
      val records = sampleRecords.copy(
        ssn = Array("123456789", "111111111", "222554444", "532081234"),
        ciRowId = Array("CID123", "CID456", "CID789", "CID101"),
        ssnConfirm = Array("1", "1", "1", "1"),
        invalidSSN = Array("0", "0", "0", "0"),
        ssnYearHigh = Array("2000", "2010", "2015", "2020"),
        ssnYearLow = Array("1980", "1990", "1995", "2000"),
        ssnIssued = Array("1980", "1990", "1995", "2000"),
        cidDeceased = Array("1", "0", "0", "0"),
        ssnDeceased = Array("0", "1", "0", "0")
      )

      val expected = records.copy(
        ssn = Array("532081234"),
        ciRowId = Array("CID101"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2020"),
        ssnYearLow = Array("2000"),
        ssnIssued = Array("2000"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("0")
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedSSNs = identityRecord.ssn.filter(ssn =>
        ssn.value.equals("123456789") || ssn.value.equals("111111111") || ssn.value.equals("222554444")
      )
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(ssn = removedSSNs),
        allAssociatedSSNs = identityRecord.allAssociatedSSNs
      )

      val actual = SerializedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      actual.updated shouldBe true
    }
  }

  "MultipleDOBMultipleSSN Tests" - {
    "case1" in {
      // removing all the ssn where the year low is less than minDob-1
      val records = sampleRecords.copy(
        dob = Array("20000515","19991212","19980101","19970101"),
        ssn = Array("532086790", "535426789"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2010", "2010"),
        ssnYearLow = Array("1995", "2001"),
        ssnIssued = Array("1995", "2001"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("1", "1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID456", "CID457")
      )
      val expected = records.copy(
        ssn = Array("535426789"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2010"),
        ssnYearLow = Array("2001"),
        ssnIssued = Array("2001"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID457")
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("532086790")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(ssn = Seq(removedSSN)), allAssociatedSSNs = identityRecord.allAssociatedSSNs)

      val actual = MultipleDOBMultipleSSN(RecordCleanupOperationInput(request, records, Some(convertToIdentityRecord(records))))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
    "case2" in {
      // null records shouldn't fail
      val records = emptySampleRecords
      val expected = emptySampleRecords
      val expectedIdentityRecord = convertToIdentityRecord(expected)
      val actual = MultipleDOBMultipleSSN(RecordCleanupOperationInput(request, records, Some(convertToIdentityRecord(records))))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
    "case3" in {
      // edge cases, min_dob=ssn+1
      val records = sampleRecords.copy(
        dob = Array("20000515","19991212","19980101","19980101"),
        ssn = Array("532086789", "535426792", "535426790", "535426791"),
        ssnConfirm = Array("1", "1", "1", "1"),
        invalidSSN = Array("0", "0", "0", "0"),
        ssnYearHigh = Array("2010", "2010", "2010", "2011"),
        ssnYearLow = Array("1995", "2001", "1997", "1998"),
        ssnIssued = Array("1995", "2001", "1997", "1998"),
        cidDeceased = Array("0", "0", "0", "0"),
        ssnDeceased = Array("1", "1", "1", "1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID456", "CID457", "CID458", "CID459")
      )
      val expected = records.copy(
        dob = Array("20000515", "19991212","19980101","19980101"),
        ssn = Array("535426792", "535426790", "535426791"),
        ssnConfirm = Array("1", "1", "1"),
        invalidSSN = Array("0", "0", "0"),
        ssnYearHigh = Array("2010", "2010", "2011"),
        ssnYearLow = Array("2001", "1997", "1998"),
        ssnIssued = Array("2001", "1997", "1998"),
        cidDeceased = Array("0", "0", "0"),
        ssnDeceased = Array("1", "1", "1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID457", "CID458", "CID459")
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("532086789")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(removed = IdentityRecordRemoved(ssn = Seq(removedSSN)), allAssociatedSSNs = identityRecord.allAssociatedSSNs)

      val actual = MultipleDOBMultipleSSN(RecordCleanupOperationInput(request, records, Some(convertToIdentityRecord(records))))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
    "case4" in {
      // all are valid, all should pass
      val records = sampleRecords.copy(
        dob = Array("19950515","19991212","19980101","19980101"),
        ssn = Array("532086789", "535426792", "535426790", "535426791"),
        ssnConfirm = Array("1", "1", "1", "1"),
        invalidSSN = Array("0", "0", "0", "0"),
        ssnYearHigh = Array("2010", "2010", "2010", "2011"),
        ssnYearLow = Array("1995", "2001", "1997", "1998"),
        ssnIssued = Array("1995", "2001", "1997", "1998"),
        cidDeceased = Array("0", "0", "0", "0"),
        ssnDeceased = Array("1", "1", "1", "1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID456", "CID457", "CID458", "CID459")
      )
      val expected = records
      val identityRecord = Some(convertToIdentityRecord(records))
      val expectedIdentityRecord = convertToIdentityRecord(expected)

      val actual = MultipleDOBMultipleSSN(RecordCleanupOperationInput(request, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
    "case5" in {
      // one dob on file is invalid and shouldn't fail
      val records = sampleRecords.copy(
        dob = Array("19950515","19991212","19980101","21540101"),
        ssn = Array("532086789", "535426792", "535426790", "535426791"),
        ssnConfirm = Array("1", "1", "1", "1"),
        invalidSSN = Array("0", "0", "0", "0"),
        ssnYearHigh = Array("2010", "2010", "2010", "2011"),
        ssnYearLow = Array("1995", "2001", "1997", "1998"),
        ssnIssued = Array("1995", "2001", "1997", "1998"),
        cidDeceased = Array("0", "0", "0", "0"),
        ssnDeceased = Array("1", "1", "1", "1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID456", "CID457", "CID458", "CID459")
      )
      val expected = records
      val identityRecord = Some(convertToIdentityRecord(records))
      val expectedIdentityRecord = convertToIdentityRecord(expected)

      val actual = MultipleDOBMultipleSSN(RecordCleanupOperationInput(request, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
    "case6" in {
      // when input ssn dont have issued and ssnutil doesnt find the year of issuance, we dont remove them.
      val records = sampleRecords.copy(
        dob = Array("19950515","19991212","19980101","21540101"),
        ssn =  Array("810482121","000002121","299813527"),
        ssnConfirm = Array("1", "1", "1", "1"),
        invalidSSN = Array("0", "0", "0", "0"),
        ssnYearHigh = Array("2010", "2010", "2010", "2011"),
        ssnYearLow = Array("1995", "2001", "1997", "1998"),
        ssnIssued = Array.empty,
        cidDeceased = Array("0", "0", "0", "0"),
        ssnDeceased = Array("1", "1", "1", "1"),
        factaCode = Array(),
        deceasedDate = Array(),
        ciRowId = Array("CID456", "CID457", "CID458", "CID459")
      )
      val expected = records
      val identityRecord = Some(convertToIdentityRecord(records))
      val expectedIdentityRecord = convertToIdentityRecord(expected)

      val actual = MultipleDOBMultipleSSN(RecordCleanupOperationInput(request, records, identityRecord))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
    }
  }

  "RandomizedSSNCleanup Tests" - {
    "case 1: should exclude randomized SSN when non-randomized SSN is present" in {
      val records = sampleRecords.copy(
        ssn = Array("532081234", "987654321"), // First is non-randomized (pre-2011), second is placeholder
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2015"),
        ssnYearLow = Array("1990", "2012"), // Second SSN is post-2011 (randomized)
        ssnIssued = Array("1995", "2012"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("0", "0")
      )

      val expected = records.copy(
        ssn = Array("532081234"), // Only non-randomized SSN kept
        ciRowId = Array("CID123"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2000"),
        ssnYearLow = Array("1990"),
        ssnIssued = Array("1995"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("0")
      )

      val identityRecord = convertToIdentityRecord(records)
      val removedSSN = identityRecord.ssn.find(_.value.equals("987654321")).get
      val expectedIdentityRecord = convertToIdentityRecord(expected).copy(
        removed = IdentityRecordRemoved(ssn = Seq(removedSSN)),
        allAssociatedSSNs = identityRecord.allAssociatedSSNs
      )

      val actual = RandomizedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe expectedIdentityRecord
      actual.updated shouldBe true
    }

    "case 2: should not exclude SSNs if all are post-2011 (both randomized)" in {
      val records = sampleRecords.copy(
        ssn = Array("987654321", "876543210"), // Both post-2011 (both randomized)
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2015", "2016"),
        ssnYearLow = Array("2012", "2013"), // Both SSNs are post-2011
        ssnIssued = Array("2012", "2013"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("0", "0")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = RandomizedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
      actual.updated shouldBe false
    }

    "case 3: should keep randomized SSN if it matches input SSN" in {
      val inputRequest = request.copy(nationalId = Some(NationalId("987654321")))
      val records = sampleRecords.copy(
        ssn = Array("532081234", "987654321"),
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2015"),
        ssnYearLow = Array("1990", "2012"),
        ssnIssued = Array("1995", "2012"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("0", "0")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = RandomizedSSNCleanup(RecordCleanupOperationInput(inputRequest, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
      actual.updated shouldBe false
    }

    "case 4: should keep randomized SSN if it matches last 4 digits of input SSN" in {
      val inputRequest = request.copy(nationalId = Some(NationalId("4321"))) // Last 4 digits
      val records = sampleRecords.copy(
        ssn = Array("532081234", "987654321"),
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2015"),
        ssnYearLow = Array("1990", "2012"),
        ssnIssued = Array("1995", "2012"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("0", "0")
      )

      val expected = records.copy(
        ssn = Array("532081234", "987654321"), // Both kept because second matches last 4 digits of input
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2015"),
        ssnYearLow = Array("1990", "2012"),
        ssnIssued = Array("1995", "2012"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("0", "0")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = RandomizedSSNCleanup(RecordCleanupOperationInput(inputRequest, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.identityRecord.get shouldBe identityRecord
      actual.updated shouldBe false
    }

    "case 5: should not exclude ITINs" in {
      val records = sampleRecords.copy(
        ssn = Array("532081234", "900701234"), // Second is ITIN (starts with 9, group 70)
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2015"),
        ssnYearLow = Array("1990", "2012"),
        ssnIssued = Array("1995", "2012"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("0", "0")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = RandomizedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
      actual.updated shouldBe false
    }

    "case 6: should exclude randomized SSN based on SSN pattern (area 700-728)" in {
      val records = sampleRecords.copy(
        ssn = Array("532081234", "700123456"), // Second has area 700 (randomized pattern)
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2010"),
        ssnYearLow = Array("1990", "2005"), // Both pre-2011, but second is randomized by pattern
        ssnIssued = Array("1995", "2005"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("0", "0")
      )

      val expected = records.copy(
        ssn = Array("532081234"),
        ciRowId = Array("CID123"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2000"),
        ssnYearLow = Array("1990"),
        ssnIssued = Array("1995"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("0")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = RandomizedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.updated shouldBe true
    }

    "case 7: should exclude randomized SSN based on SSN pattern (area 773-899)" in {
      val records = sampleRecords.copy(
        ssn = Array("532081234", "800123456"), // Second has area 800 (randomized pattern)
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2010"),
        ssnYearLow = Array("1990", "2005"), // Both pre-2011, but second is randomized by pattern
        ssnIssued = Array("1995", "2005"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("0", "0")
      )

      val expected = records.copy(
        ssn = Array("532081234"),
        ciRowId = Array("CID123"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2000"),
        ssnYearLow = Array("1990"),
        ssnIssued = Array("1995"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("0")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = RandomizedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.updated shouldBe true
    }

    "case 8: should exclude randomized SSN based on SSN pattern (area 734-749)" in {
      val records = sampleRecords.copy(
        ssn = Array("532081234", "740123456"), // Second has area 740 (randomized pattern)
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2010"),
        ssnYearLow = Array("1990", "2005"), // Both pre-2011, but second is randomized by pattern
        ssnIssued = Array("1995", "2005"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("0", "0")
      )

      val expected = records.copy(
        ssn = Array("532081234"),
        ciRowId = Array("CID123"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2000"),
        ssnYearLow = Array("1990"),
        ssnIssued = Array("1995"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("0")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = RandomizedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(expected)
      actual.updated shouldBe true
    }

    "case 9: should not modify records if only one SSN is present" in {
      val records = sampleRecords.copy(
        ssn = Array("987654321"), // Only one SSN, even if randomized
        ciRowId = Array("CID123"),
        ssnConfirm = Array("1"),
        invalidSSN = Array("0"),
        ssnYearHigh = Array("2015"),
        ssnYearLow = Array("2012"),
        ssnIssued = Array("2012"),
        cidDeceased = Array("0"),
        ssnDeceased = Array("0")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = RandomizedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
      actual.updated shouldBe false
    }

    "case 10: should not modify records if all SSNs are non-randomized" in {
      val records = sampleRecords.copy(
        ssn = Array("532081234", "563454721"), // Both non-randomized
        ciRowId = Array("CID123", "CID456"),
        ssnConfirm = Array("1", "1"),
        invalidSSN = Array("0", "0"),
        ssnYearHigh = Array("2000", "2010"),
        ssnYearLow = Array("1990", "2000"), // Both pre-2011 and non-randomized patterns
        ssnIssued = Array("1995", "2005"),
        cidDeceased = Array("0", "0"),
        ssnDeceased = Array("0", "0")
      )

      val identityRecord = convertToIdentityRecord(records)

      val actual = RandomizedSSNCleanup(RecordCleanupOperationInput(request, records, Some(identityRecord)))
      Serialization.write(actual.records) shouldBe Serialization.write(records)
      actual.identityRecord.get shouldBe identityRecord
      actual.updated shouldBe false
    }
  }
}
